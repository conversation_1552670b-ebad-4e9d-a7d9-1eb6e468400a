<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#9C27B0" opacity="0.1"/>
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#7B1FA2">案例四：痛点补充与复盘总结</text>
  
  <!-- 痛点补充 -->
  <rect x="80" y="160" width="1760" height="200" fill="#FFF3E0" stroke="#FFB74D" stroke-width="2" rx="10"/>
  <text x="100" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#F57C00">痛点补充</text>
  <text x="120" y="250" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">3. 扩展困难</text>
  <text x="140" y="290" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 计划添置更多智能设备</text>
  <text x="140" y="330" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 现有网络架构已不堪重负，无法满足未来需求</text>
  
  <!-- 复盘总结区域 -->
  <rect x="80" y="400" width="1760" height="600" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="10"/>
  <text x="100" y="440" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1976D2">复盘总结</text>
  
  <text x="120" y="500" font-family="Microsoft YaHei" font-size="26" fill="#333333">此案例的成功之处在于采用了场景化的营销策略。</text>
  
  <text x="120" y="560" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1976D2">场景化营销三步法：</text>
  <text x="140" y="600" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 从智能家居、远程办公等具体场景切入</text>
  <text x="140" y="640" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 自然地引出网络痛点，降低客户的抵触情绪</text>
  <text x="140" y="680" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 利用设备负载测试、信号热力图等工具数据化展示</text>
  
  <text x="120" y="740" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1976D2">数据化说服策略：</text>
  <text x="140" y="780" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 将方案优势数据化、可视化，增强说服力</text>
  <text x="140" y="820" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 通过引导客户思考未来智能家居的扩展需求</text>
  <text x="140" y="860" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 激发其前瞻性思考，推动即时决策</text>
  
  <text x="120" y="920" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1976D2">核心成功要素：</text>
  <text x="140" y="960" font-family="Microsoft YaHei" font-size="24" fill="#555555">场景化切入 + 数据化展示 + 前瞻性引导 = 高效成交</text>
  
  <!-- 装饰元素 -->
  <circle cx="1700" cy="300" r="100" fill="#9C27B0" opacity="0.1"/>
  <circle cx="1700" cy="300" r="60" fill="#9C27B0" opacity="0.2"/>
  <text x="1700" y="290" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#7B1FA2">场景化</text>
  <text x="1700" y="310" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#7B1FA2">营销</text>
  
  <circle cx="1700" cy="700" r="80" fill="#2196F3" opacity="0.1"/>
  <circle cx="1700" cy="700" r="40" fill="#2196F3" opacity="0.2"/>
  <text x="1700" y="710" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#1976D2">数据说服</text>
</svg>
