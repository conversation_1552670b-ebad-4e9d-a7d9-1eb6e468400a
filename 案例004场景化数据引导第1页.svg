<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#9C27B0" opacity="0.1"/>
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#7B1FA2">案例四：场景化切入，用数据和前瞻性引导打动客户</text>
  
  <!-- 客户背景区域 -->
  <rect x="80" y="160" width="800" height="300" fill="#F8F9FA" stroke="#E9ECEF" stroke-width="2" rx="10"/>
  <text x="100" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#7B1FA2">客户背景</text>
  <text x="120" y="250" font-family="Microsoft YaHei" font-size="24" fill="#333333">• 外场拦截到的用户</text>
  <text x="120" y="290" font-family="Microsoft YaHei" font-size="24" fill="#333333">• 上门检测后发现使用友商宽带</text>
  <text x="120" y="330" font-family="Microsoft YaHei" font-size="24" fill="#333333">• 家中智能设备多（超过10台）</text>
  <text x="120" y="370" font-family="Microsoft YaHei" font-size="24" fill="#333333">• 摄像头、智能音箱等设备齐全</text>
  <text x="120" y="410" font-family="Microsoft YaHei" font-size="24" fill="#333333">• 有远程办公需求</text>
  
  <!-- 痛点分析区域 -->
  <rect x="920" y="160" width="920" height="300" fill="#FFF3E0" stroke="#FFB74D" stroke-width="2" rx="10"/>
  <text x="940" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#F57C00">痛点分析</text>
  <text x="960" y="250" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">1. 覆盖盲区</text>
  <text x="980" y="290" font-family="Microsoft YaHei" font-size="22" fill="#555555">路由器信号穿墙后严重衰减</text>
  <text x="980" y="320" font-family="Microsoft YaHei" font-size="22" fill="#555555">次卧、卫生间等区域信号极弱，视频会议频繁卡顿</text>
  
  <text x="960" y="360" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">2. 连接不稳</text>
  <text x="980" y="400" font-family="Microsoft YaHei" font-size="22" fill="#555555">多设备同时在线时，网络波动大</text>
  <text x="980" y="430" font-family="Microsoft YaHei" font-size="22" fill="#555555">智能家居设备经常离线</text>
  
  <!-- 解决方案区域 -->
  <rect x="80" y="500" width="1760" height="300" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="10"/>
  <text x="100" y="540" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E7D32">解决方案</text>
  <text x="120" y="590" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">推荐"智能全屋FTTR"组网方案：</text>
  <text x="140" y="630" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 通过"主路由+子路由"的模式</text>
  <text x="140" y="670" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 实现信号的分层精准覆盖，彻底消除盲区</text>
  <text x="140" y="710" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 利用FTTR的高并发技术优势</text>
  <text x="140" y="750" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 保障100台以上设备同时稳定连接</text>
  <text x="140" y="790" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 满足客户当下及未来的高带宽应用需求</text>
  
  <!-- 项目成果预览 -->
  <rect x="80" y="840" width="1760" height="160" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2" rx="10"/>
  <text x="100" y="880" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#7B1FA2">项目成果</text>
  <text x="120" y="920" font-family="Microsoft YaHei" font-size="24" fill="#333333">客户当场决定办理199元月费套餐及699元设备礼包</text>
  <text x="120" y="960" font-family="Microsoft YaHei" font-size="24" fill="#333333">实现了家庭网络的全屋无缝覆盖</text>
  
  <!-- 装饰元素 -->
  <circle cx="1750" cy="350" r="80" fill="#9C27B0" opacity="0.1"/>
  <circle cx="1750" cy="350" r="40" fill="#9C27B0" opacity="0.2"/>
  <text x="1750" y="360" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#7B1FA2">智能家居</text>
</svg>
