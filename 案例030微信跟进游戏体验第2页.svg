<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#673AB7" opacity="0.1"/>
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#512DA8">案例三十：解决方案详细与复盘总结</text>
  
  <!-- 解决方案详细 -->
  <rect x="80" y="160" width="1760" height="280" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="10"/>
  <text x="100" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E7D32">解决方案详细</text>
  
  <text x="120" y="250" font-family="Microsoft YaHei" font-size="24" fill="#333333">营销人员没有泛泛地介绍产品参数，而是精准地针对"游戏"这一场景进行沟通：</text>
  
  <text x="140" y="300" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 强调FTTR"全屋光纤、无缝切换"的特性</text>
  <text x="140" y="340" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 能确保"打游戏不卡顿、不掉线"</text>
  <text x="140" y="380" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 直击用户内心最在意的需求</text>
  
  <!-- 复盘总结区域 -->
  <rect x="80" y="480" width="1760" height="520" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="10"/>
  <text x="100" y="520" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1976D2">复盘总结</text>
  
  <text x="120" y="580" font-family="Microsoft YaHei" font-size="26" fill="#333333">维护好与老客户的微信联系，是孵化商机的良田。</text>
  
  <text x="120" y="640" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1976D2">客户主动咨询的价值：</text>
  <text x="140" y="680" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 当客户主动咨询时，往往是转化率最高的时机</text>
  <text x="140" y="720" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 说明客户已有明确需求和信任基础</text>
  
  <text x="120" y="780" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1976D2">精准营销策略：</text>
  <text x="140" y="820" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 必须将产品的技术优势（如无缝切换）</text>
  <text x="140" y="860" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 精准地翻译成客户能切身感受到的利益（如游戏不掉线）</text>
  <text x="140" y="900" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 对话要聚焦于客户的兴趣点和使用场景</text>
  <text x="140" y="940" font-family="Microsoft YaHei" font-size="24" fill="#555555">• 而不是我们自己的产品参数</text>
  
  <!-- 装饰元素 -->
  <circle cx="1700" cy="320" r="100" fill="#673AB7" opacity="0.1"/>
  <circle cx="1700" cy="320" r="60" fill="#673AB7" opacity="0.2"/>
  <text x="1700" y="310" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#512DA8">场景化</text>
  <text x="1700" y="330" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#512DA8">沟通</text>
  
  <circle cx="1700" cy="750" r="80" fill="#2196F3" opacity="0.1"/>
  <circle cx="1700" cy="750" r="40" fill="#2196F3" opacity="0.2"/>
  <text x="1700" y="760" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#1976D2">微信营销</text>
</svg>
